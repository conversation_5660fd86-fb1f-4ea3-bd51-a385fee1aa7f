"""
Диагностический тест для проверки генерации множественных walk-forward шагов.
"""

import pytest
import pandas as pd
import os
import sys
import yaml

# Добавляем путь к src для импорта модулей
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from optimiser.fast_objective import FastWalkForwardObjective
from yaml_config import load_config


def test_walk_forward_steps_debug():
    """Диагностический тест для проверки генерации шагов."""
    
    # Создаем тестовый файл пар
    pairs_data = pd.DataFrame({
        's1': ['AAPL', 'MSFT'],
        's2': ['MSFT', 'GOOGL'],
        'beta': [1.2, 0.8],
        'mean': [0.0, 0.0],
        'std': [1.0, 1.0]
    })
    
    os.makedirs('outputs', exist_ok=True)
    pairs_data.to_csv('outputs/preselected_pairs.csv', index=False)
    
    # Создаем тестовую конфигурацию
    config_dict = load_config('configs/main_2024.yaml')
    config_dict['walk_forward']['start_date'] = "2024-01-01"
    config_dict['walk_forward']['end_date'] = "2024-01-31"
    config_dict['walk_forward']['training_period_days'] = 30
    config_dict['walk_forward']['testing_period_days'] = 10
    config_dict['walk_forward']['step_size_days'] = 5  # Маленький шаг
    
    # Сохраняем тестовую конфигурацию
    with open('test_config_debug.yaml', 'w') as f:
        yaml.dump(config_dict, f)
    
    try:
        # Создаем objective
        objective = FastWalkForwardObjective('test_config_debug.yaml', 'configs/search_space_fast.yaml')
        
        # Проверяем конфигурацию
        print(f"Start date: {objective.base_config.walk_forward.start_date}")
        print(f"End date: {getattr(objective.base_config.walk_forward, 'end_date', 'NOT SET')}")
        print(f"Step size days: {getattr(objective.base_config.walk_forward, 'step_size_days', 'NOT SET')}")
        print(f"Training period days: {objective.base_config.walk_forward.training_period_days}")
        print(f"Testing period days: {objective.base_config.walk_forward.testing_period_days}")
        
        # Тестируем генерацию шагов напрямую
        cfg = objective.base_config
        start_date = pd.to_datetime(cfg.walk_forward.start_date)
        end_date = pd.to_datetime(getattr(cfg.walk_forward, 'end_date', start_date + pd.Timedelta(days=cfg.walk_forward.testing_period_days)))
        step_size_days = getattr(cfg.walk_forward, 'step_size_days', cfg.walk_forward.testing_period_days)
        
        print(f"\nПараметры генерации:")
        print(f"  start_date: {start_date}")
        print(f"  end_date: {end_date}")
        print(f"  step_size_days: {step_size_days}")
        
        # Генерируем шаги
        walk_forward_steps = []
        current_test_start = start_date
        bar_minutes = getattr(cfg.pair_selection, "bar_minutes", None) or 15
        bar_delta = pd.Timedelta(minutes=bar_minutes)
        
        step_count = 0
        while current_test_start < end_date:
            training_start = current_test_start - pd.Timedelta(days=cfg.walk_forward.training_period_days)
            training_end = current_test_start - bar_delta
            testing_start = current_test_start
            testing_end = min(
                testing_start + pd.Timedelta(days=cfg.walk_forward.testing_period_days),
                end_date
            )
            
            print(f"\nШаг {step_count + 1}:")
            print(f"  current_test_start: {current_test_start}")
            print(f"  testing_start: {testing_start}")
            print(f"  testing_end: {testing_end}")
            print(f"  training_start: {training_start}")
            print(f"  training_end: {training_end}")
            
            # Проверяем что тестовый период не пустой
            if testing_end > testing_start:
                walk_forward_steps.append({
                    'training_start': training_start,
                    'training_end': training_end,
                    'testing_start': testing_start,
                    'testing_end': testing_end
                })
                step_count += 1
            else:
                print(f"  ПРОПУЩЕН: testing_end <= testing_start")
                break
            
            current_test_start += pd.Timedelta(days=step_size_days)
            print(f"  next current_test_start: {current_test_start}")
            
            if step_count > 10:  # Защита от бесконечного цикла
                print("  ОСТАНОВЛЕНО: слишком много шагов")
                break
        
        print(f"\nВСЕГО СГЕНЕРИРОВАНО ШАГОВ: {len(walk_forward_steps)}")
        
        # Проверяем что генерируется больше одного шага
        assert len(walk_forward_steps) > 1, f"Ожидалось больше 1 шага, получено {len(walk_forward_steps)}"
        
        print("✅ ТЕСТ ПРОШЕЛ: Генерируется множественные walk-forward шаги")

        # Теперь тестируем полный цикл обработки
        print("\n" + "="*50)
        print("ТЕСТИРОВАНИЕ ПОЛНОГО ЦИКЛА ОБРАБОТКИ")
        print("="*50)

        # Мокаем методы для отслеживания вызовов
        call_count = 0

        def counting_process_step(*args, **kwargs):
            nonlocal call_count
            call_count += 1
            print(f"  📊 _process_single_walk_forward_step вызван {call_count} раз")
            return {
                'pnls': [pd.Series([0.1, 0.2], index=pd.date_range('2024-01-01', periods=2, freq='15min'))],
                'trades': 5,
                'pairs_checked': 2,
                'pairs_with_data': 1
            }

        def mock_load_data(*args, **kwargs):
            print(f"  📂 _load_data_for_step вызван с аргументами: {args}")
            return {'full_data': pd.DataFrame({'AAPL': [100, 101], 'MSFT': [200, 201]})}

        # Заменяем методы
        original_process_step = objective._process_single_walk_forward_step
        original_load_data = objective._load_data_for_step

        objective._process_single_walk_forward_step = counting_process_step
        objective._load_data_for_step = mock_load_data

        # Запускаем бэктест
        params = {'zscore_threshold': 2.0}
        print(f"\n🚀 Запускаем _run_fast_backtest с параметрами: {params}")
        result = objective._run_fast_backtest(params)

        print(f"\n📊 РЕЗУЛЬТАТЫ:")
        print(f"  Количество вызовов _process_single_walk_forward_step: {call_count}")
        print(f"  Ожидалось: {len(walk_forward_steps)}")
        print(f"  Результат бэктеста: {result}")

        # Восстанавливаем оригинальные методы
        objective._process_single_walk_forward_step = original_process_step
        objective._load_data_for_step = original_load_data

        # Проверяем что все шаги были обработаны
        assert call_count == len(walk_forward_steps), \
            f"Ожидалось {len(walk_forward_steps)} вызовов, получено {call_count}"

        print("✅ ПОЛНЫЙ ТЕСТ ПРОШЕЛ: Все walk-forward шаги обрабатываются")
        
    finally:
        # Очищаем тестовые файлы
        for file in ['outputs/preselected_pairs.csv', 'test_config_debug.yaml']:
            if os.path.exists(file):
                os.remove(file)


if __name__ == "__main__":
    test_walk_forward_steps_debug()
