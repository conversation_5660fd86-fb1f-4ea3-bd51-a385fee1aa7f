#!/usr/bin/env python3
"""
Тесты для критических исправлений в Optuna оптимизации (вторая итерация).
Проверяет исправления дублирования переменных и безопасного доступа к конфигурации.
"""

import pytest
import sys
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
import pandas as pd
import numpy as np
import optuna

# Добавляем корневую директорию в путь
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.optimiser.fast_objective import FastWalkForwardObjective
from src.optimiser.metric_utils import validate_params, normalize_params


class TestOptunaCriticalFixes2:
    """Тесты для критических исправлений в Optuna оптимизации (вторая итерация)."""
    
    def setup_method(self):
        """Настройка для каждого теста."""
        # Создаем mock файл preselected_pairs.csv
        self.mock_pairs = pd.DataFrame({
            's1': ['AAPL', 'MSFT', 'GOOGL'],
            's2': ['TSLA', 'NVDA', 'META'],
            'beta': [1.2, 0.8, 1.5],
            'mean': [0.1, -0.05, 0.2],
            'std': [0.15, 0.12, 0.18],
            'half_life': [5.0, 7.2, 4.8],
            'pvalue': [0.01, 0.02, 0.005],
            'mean_crossings': [15, 20, 12]
        })
        
    @patch('pandas.read_csv')
    @patch('pathlib.Path.exists')
    def test_trial_number_no_duplication(self, mock_exists, mock_read_csv):
        """
        Тест: Проверяет, что trial_number не дублируется в коде.
        Исправление: Убрано переопределение trial_number в строке логирования.
        """
        mock_exists.return_value = True
        mock_read_csv.return_value = self.mock_pairs
        
        objective = FastWalkForwardObjective(
            'configs/main_2024.yaml',
            'configs/search_space_fast.yaml'
        )
        
        # Создаем mock trial
        mock_trial = Mock()
        mock_trial.number = 42
        mock_trial.suggest_float = Mock(side_effect=[2.0, 0.5])
        mock_trial.suggest_int = Mock(return_value=20)
        mock_trial.set_user_attr = Mock()
        
        # Mock методов бэктеста
        mock_metrics = {
            'sharpe_ratio_abs': 1.5,
            'total_trades': 10,
            'max_drawdown': 0.1,
            'positive_days_rate': 0.6,
            'win_rate': 0.55
        }
        
        with patch.object(objective, '_run_fast_backtest_with_reports', return_value=mock_metrics):
            with patch.object(objective, '_suggest_parameters', return_value={
                'zscore_threshold': 2.0,
                'zscore_exit': 0.5,
                'rolling_window': 20,
                'stop_loss_multiplier': 3.0,
                'max_active_positions': 5,
                'risk_per_position_pct': 0.02,
                'max_position_size_pct': 0.1,
                'trial_number': 42
            }):
                # Вызываем objective - не должно быть ошибок с дублированием trial_number
                result = objective(mock_trial)
                
                # Проверяем, что результат корректный
                assert isinstance(result, (int, float))
                assert not np.isnan(result)
                assert not np.isinf(result)
                
                # Проверяем, что set_user_attr был вызван (логирование работает)
                assert mock_trial.set_user_attr.called
                
    def test_safe_config_access(self):
        """
        Тест: Проверяет безопасный доступ к секциям конфигурации.
        Исправление: Добавлены проверки hasattr() перед setattr().
        """
        # Создаем mock конфигурации без некоторых секций
        mock_cfg = Mock()
        mock_cfg.portfolio = Mock()
        mock_cfg.portfolio.max_active_positions = 5
        # Намеренно НЕ добавляем pair_selection, backtest, data_processing

        test_params = {
            'ssd_top_n': 5000,  # Должно быть проигнорировано (нет pair_selection)
            'zscore_threshold': 2.0,  # Должно быть проигнорировано (нет backtest)
            'max_active_positions': 10,  # Должно работать (есть portfolio)
            'normalization_method': 'zscore'  # Должно быть проигнорировано (нет data_processing)
        }

        # Тестируем логику применения параметров напрямую
        try:
            # Симулируем код из _run_fast_backtest_with_reports
            for key, value in test_params.items():
                if key in ["ssd_top_n", "kpss_pvalue_threshold", "coint_pvalue_threshold",
                          "min_half_life_days", "max_half_life_days", "min_mean_crossings"]:
                    if hasattr(mock_cfg, 'pair_selection'):
                        setattr(mock_cfg.pair_selection, key, value)
                elif key in ["zscore_threshold", "zscore_exit", "rolling_window", "stop_loss_multiplier",
                            "time_stop_multiplier", "cooldown_hours", "commission_pct", "slippage_pct"]:
                    if hasattr(mock_cfg, 'backtest'):
                        setattr(mock_cfg.backtest, key, value)
                elif key in ["max_active_positions", "risk_per_position_pct", "max_position_size_pct"]:
                    if hasattr(mock_cfg, 'portfolio'):
                        setattr(mock_cfg.portfolio, key, value)
                elif key in ["normalization_method", "min_history_ratio"]:
                    if hasattr(mock_cfg, 'data_processing'):
                        setattr(mock_cfg.data_processing, key, value)

            success = True
        except AttributeError as e:
            success = False
            pytest.fail(f"AttributeError при применении параметров: {e}")

        assert success, "Применение параметров должно работать без AttributeError"

        # Проверяем, что параметр portfolio был применен (секция существует)
        assert mock_cfg.portfolio.max_active_positions == 10

        # Основная проверка: код должен работать без AttributeError
        # Это означает, что hasattr() проверки работают корректно
                
    def test_safe_data_processing_access(self):
        """
        Тест: Проверяет безопасный доступ к cfg.data_processing в нормализации.
        Исправление: Добавлены проверки hasattr() и значения по умолчанию.
        """
        # Создаем простой объект БЕЗ data_processing секции
        class MockConfig:
            def __init__(self):
                self.portfolio = type('obj', (object,), {'initial_capital': 10000})()
                # Намеренно НЕ добавляем data_processing

        mock_cfg = MockConfig()

        # Тестируем логику безопасного доступа к data_processing напрямую
        try:
            # Симулируем код из _backtest_single_pair
            norm_method = getattr(mock_cfg.data_processing, 'normalization_method', 'minmax') if hasattr(mock_cfg, 'data_processing') else 'minmax'
            min_hist_ratio = getattr(mock_cfg.data_processing, 'min_history_ratio', 0.8) if hasattr(mock_cfg, 'data_processing') else 0.8
            handle_const = getattr(mock_cfg.data_processing, 'handle_constant', True) if hasattr(mock_cfg, 'data_processing') else True
            fill_method = getattr(mock_cfg.data_processing, 'fill_method', 'linear') if hasattr(mock_cfg, 'data_processing') else 'linear'

            success = True
        except AttributeError as e:
            success = False
            pytest.fail(f"AttributeError при доступе к data_processing: {e}")

        assert success, "Доступ к data_processing должен работать без AttributeError"

        # Проверяем, что используются дефолтные значения
        assert norm_method == 'minmax', "Должно использоваться дефолтное значение normalization_method"
        assert min_hist_ratio == 0.8, "Должно использоваться дефолтное значение min_history_ratio"
        assert handle_const == True, "Должно использоваться дефолтное значение handle_constant"
        assert fill_method == 'linear', "Должно использоваться дефолтное значение fill_method"
            
    def test_parameter_validation_edge_cases(self):
        """
        Тест: Проверяет валидацию параметров на граничных случаях.
        Проверяет, что все критические ошибки валидации обрабатываются корректно.
        """
        # Тест 1: Отрицательный zscore_threshold
        with pytest.raises(ValueError, match="z_entry должен быть положительным"):
            validate_params({'zscore_threshold': -1.0, 'zscore_exit': 0.5})

        # Тест 2: Отрицательный stop_loss_multiplier
        with pytest.raises(ValueError, match="stop_loss_multiplier должен быть неотрицательным"):
            validate_params({'stop_loss_multiplier': -1.0})

        # Тест 3: max_active_positions < 1
        with pytest.raises(ValueError, match="max_active_positions должен быть >= 1"):
            validate_params({'max_active_positions': 0})

        # Тест 4: Некорректный max_position_size_pct
        with pytest.raises(ValueError, match="max_position_size_pct должен быть в \\(0, 1\\]"):
            validate_params({'max_position_size_pct': 1.5})

        # Тест 5: Некорректный risk_per_position_pct
        with pytest.raises(ValueError, match="risk_per_position_pct должен быть в \\(0, 1\\]"):
            validate_params({'risk_per_position_pct': 0.0})

        # Тест 6: Проверяем, что функция исправляет некорректные значения
        result = validate_params({'zscore_threshold': 2.0, 'zscore_exit': 2.5})
        assert result['zscore_exit'] < result['zscore_threshold'], "zscore_exit должен быть исправлен"

    def test_unreachable_code_fix(self):
        """
        Тест: Проверяет исправление недостижимого кода в обработке ошибок.
        Убеждается, что логика if-else работает корректно для разных типов входных данных.
        """
        config_path = Path(__file__).parent.parent / "configs" / "main_2024.yaml"
        search_space_path = Path(__file__).parent.parent / "configs" / "search_space_fast.yaml"

        objective = FastWalkForwardObjective(str(config_path), str(search_space_path))

        # Тест 1: Невалидные параметры с обычным словарем (не Optuna trial)
        invalid_params = {'zscore_threshold': -1.0, 'zscore_exit': 0.5}

        # Мокаем validate_params чтобы выбросить ошибку
        with patch('src.optimiser.fast_objective.validate_params') as mock_validate:
            mock_validate.side_effect = ValueError("Тестовая ошибка валидации")

            result = objective(invalid_params)
            assert result == -5.0, f"Должен вернуть PENALTY_SOFT (-5.0), получен: {result}"

        # Тест 2: Невалидный Sharpe ratio с обычным словарем
        valid_params = {'zscore_threshold': 2.0, 'zscore_exit': 0.5}

        with patch.object(objective, '_run_fast_backtest') as mock_backtest:
            mock_backtest.return_value = {'sharpe_ratio_abs': None, 'total_trades': 10}

            result = objective(valid_params)
            assert result == -5.0, f"Должен вернуть PENALTY_SOFT (-5.0), получен: {result}"

        # Тест 3: Недостаточно сделок с обычным словарем
        with patch.object(objective, '_run_fast_backtest') as mock_backtest:
            mock_backtest.return_value = {'sharpe_ratio_abs': 1.5, 'total_trades': 2}

            result = objective(valid_params)
            assert result == -5.0, f"Должен вернуть PENALTY_SOFT (-5.0), получен: {result}"

    def test_comprehensive_critical_fixes_validation(self):
        """
        Тест: Комплексная проверка всех критических исправлений.
        Проверяет, что все найденные критические ошибки действительно исправлены.
        """
        config_path = Path(__file__).parent.parent / "configs" / "main_2024.yaml"
        search_space_path = Path(__file__).parent.parent / "configs" / "search_space_fast.yaml"

        objective = FastWalkForwardObjective(str(config_path), str(search_space_path))

        # Проверка 1: Нет дублирования переменной trial_number
        # Это проверяется статически - код должен компилироваться без ошибок

        # Проверка 2: Безопасный доступ к конфигурации
        test_params = {
            'zscore_threshold': 2.0,
            'zscore_exit': 0.5,
            'ssd_top_n': 1500,  # Валидное значение >= 1000
            'normalization_method': 'minmax',
            'min_history_ratio': 0.8
        }

        # Мокаем бэктест для изоляции тестирования логики
        with patch.object(objective, '_run_fast_backtest') as mock_backtest:
            mock_backtest.return_value = {
                'sharpe_ratio_abs': 1.2,
                'total_trades': 50,
                'max_drawdown': 0.1,
                'positive_days_rate': 0.6
            }

            # Должно работать без ошибок доступа к атрибутам
            result = objective(test_params)
            assert isinstance(result, (int, float)), f"Результат должен быть числом, получен: {type(result)}"
            assert result > -10, f"Результат не должен быть слишком отрицательным: {result}"

        # Проверка 3: Правильная обработка недостижимого кода
        # Уже проверено в test_unreachable_code_fix

        # Проверка 4: Валидация параметров работает корректно
        valid_result = validate_params(test_params)
        assert 'zscore_threshold' in valid_result
        assert 'zscore_exit' in valid_result
        assert valid_result['zscore_exit'] < valid_result['zscore_threshold']

        # Проверка 5: Нормализация параметров работает корректно
        short_params = {
            'z_entry': 2.0,
            'z_exit': 0.5,
            'sl_mult': 3.0,
            'risk_per_pos': 0.02
        }
        normalized = normalize_params(short_params)
        assert 'zscore_threshold' in normalized
        assert 'zscore_exit' in normalized
        assert 'stop_loss_multiplier' in normalized
        assert 'risk_per_position_pct' in normalized
            
    def test_normalize_params_completeness(self):
        """
        Тест: Проверяет полноту нормализации коротких имен параметров.
        Убеждается, что все короткие имена корректно маппятся на канонические.
        """
        short_params = {
            'z_entry': 2.5,
            'z_exit': 0.3,
            'sl_mult': 4.0,
            'time_stop_mult': 2.5,  # Исправлено: правильное короткое имя
            'max_active_pos': 8,    # Исправлено: правильное короткое имя
            'max_pos_size': 0.15,   # Исправлено: правильное короткое имя
            'risk_per_pos': 0.025   # Исправлено: правильное короткое имя
        }
        
        normalized = normalize_params(short_params)
        
        # Проверяем, что все короткие имена были заменены
        expected_canonical = {
            'zscore_threshold': 2.5,
            'zscore_exit': 0.3,
            'stop_loss_multiplier': 4.0,
            'time_stop_multiplier': 2.5,
            'max_active_positions': 8,
            'max_position_size_pct': 0.15,
            'risk_per_position_pct': 0.025
        }
        
        for canonical_name, expected_value in expected_canonical.items():
            assert canonical_name in normalized, f"Отсутствует канонический параметр: {canonical_name}"
            assert normalized[canonical_name] == expected_value, f"Неверное значение для {canonical_name}"
            
        # Проверяем, что коротких имен больше нет
        short_names = ['z_entry', 'z_exit', 'sl_mult', 'time_stop_mult', 'max_active_pos', 'max_pos_size', 'risk_per_pos']
        for short_name in short_names:
            assert short_name not in normalized, f"Короткое имя {short_name} не было заменено"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
